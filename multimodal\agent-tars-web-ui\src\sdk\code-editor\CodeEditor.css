/**
 * Code Editor Dark IDE Theme Styles
 * 
 * IMPORTANT: These styles are scoped specifically for the CodeEditor component
 * and should NOT affect MarkdownRenderer or any other components. All styles
 * use the .code-editor- prefix to ensure proper isolation.
 * 
 * This component uses a deep dark IDE theme for a more professional appearance.
 */

.code-editor-container {
  font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
  height: 100%;
}

.code-editor-wrapper {
  background: #0d1117;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #21262d;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
}

/* IDE-style header with deeper dark theme */
.code-editor-header {
  background: #010409;
  border-bottom: 1px solid #21262d;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 40px;
}

.code-editor-header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* Browser-style control buttons */
.code-editor-controls {
  display: flex;
  gap: 6px;
}

.code-editor-control-btn {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
}

.code-editor-control-red {
  background: #ff5f57;
  border: 1px solid #e0443e;
}

.code-editor-control-yellow {
  background: #ffbd2e;
  border: 1px solid #dea123;
}

.code-editor-control-green {
  background: #28ca42;
  border: 1px solid #24a134;
}

/* File info section */
.code-editor-file-info {
  position: relative;
  cursor: default;
  padding: 4px 2px; /* Add some padding for easier hover */
}

.code-editor-file-info:hover {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.code-editor-file-name {
  color: #f0f6fc;
  font-size: 13px;
  font-weight: 500;
  font-family: inherit;
}

/* Language badge */
.code-editor-language-badge {
  background: #21262d;
  color: #7d8590;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 1px solid #30363d;
}

/* Actions */
.code-editor-actions {
  display: flex;
  gap: 4px;
}

.code-editor-action-btn {
  padding: 6px;
  border-radius: 4px;
  background: transparent;
  border: none;
  color: #7d8590;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.code-editor-action-btn:hover {
  background: #21262d;
  color: #f0f6fc;
}

/* Tooltip styles */
.code-editor-tooltip {
  position: absolute;
  top: 100%;
  left: 0;
  margin-top: 12px; /* Increased gap for easier mouse movement */
  z-index: 50;
  background: #161b22;
  color: #f0f6fc;
  font-size: 12px;
  border-radius: 6px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
  border: 1px solid #30363d;
  min-width: max-content;
  max-width: 300px;
  /* Add invisible bridge area for easier mouse movement */
  padding-top: 8px;
}

.code-editor-tooltip::before {
  content: '';
  position: absolute;
  top: -12px;
  left: 0;
  right: 0;
  height: 12px;
  background: transparent;
  /* Invisible interaction bridge */
}

.code-editor-tooltip-content {
  padding: 12px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: -8px; /* Compensate for the padding-top */
}

.code-editor-tooltip-section {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.code-editor-tooltip-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding-top: 8px;
  border-top: 1px solid #30363d;
}

.code-editor-tooltip-icon {
  color: #7d8590;
  flex-shrink: 0;
  margin-top: 2px;
}

.code-editor-tooltip-label {
  color: #7d8590;
  font-weight: 500;
  margin-bottom: 4px;
}

.code-editor-tooltip-value {
  color: #f0f6fc;
  font-family: inherit;
  word-break: break-all;
  line-height: 1.4;
}

.code-editor-tooltip-btn {
  margin-top: 8px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: #21262d;
  color: #f0f6fc;
  border: 1px solid #30363d;
  border-radius: 4px;
  font-size: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.code-editor-tooltip-btn:hover {
  background: #30363d;
  color: #fff;
}

.code-editor-tooltip-arrow {
  position: absolute;
  top: -4px;
  left: 16px;
  width: 8px;
  height: 8px;
  background: #161b22;
  border-left: 1px solid #30363d;
  border-top: 1px solid #30363d;
  transform: rotate(45deg);
}

/* Code content area */
.code-editor-content {
  background: #0d1117;
  overflow: auto;
}

.code-editor-inner {
  display: flex;
  min-height: 100%;
}

/* Line numbers */
.code-editor-line-numbers {
  background: #010409;
  border-right: 1px solid #21262d;
  flex-shrink: 0;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.code-editor-line-numbers-inner {
  padding: 12px;
  color: #484f58;
  font-size: 13px;
  line-height: 1.5;
  text-align: right;
  min-width: 2rem;
}

.code-editor-line-number {
  height: 19.5px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

/* Code area */
.code-editor-code-area {
  flex: 1;
  overflow: auto;
}

.code-editor-pre {
  margin: 0;
  padding: 12px 16px;
  background: transparent;
  color: #f0f6fc;
  font-size: 13px;
  line-height: 1.5;
  overflow: visible;
  tab-size: 2;
  -moz-tab-size: 2;
  -o-tab-size: 2;
}

.code-editor-code {
  background: transparent !important;
  color: #f0f6fc;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

/* Status bar */
.code-editor-status-bar {
  background: #010409;
  border-top: 1px solid #21262d;
  padding: 4px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
  color: #7d8590;
  min-height: 24px;
}

.code-editor-status-left,
.code-editor-status-right {
  display: flex;
  gap: 16px;
}

.code-editor-status-item {
  color: #7d8590;
}

/* Scrollbar styling for deeper dark theme */
.code-editor-container ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.code-editor-container ::-webkit-scrollbar-track {
  background: #161b22;
  border-radius: 4px;
}

.code-editor-container ::-webkit-scrollbar-thumb {
  background: #484f58;
  border-radius: 4px;
}

.code-editor-container ::-webkit-scrollbar-thumb:hover {
  background: #6e7681;
}

/* Selection styling */
.code-editor-container ::selection {
  background-color: rgba(99, 102, 241, 0.25);
}

/* Focus styles */
.code-editor-container:focus-within {
  outline: none;
}

/* Enhanced dark theme syntax highlighting */
.code-editor-code .hljs {
  background: transparent !important;
  color: #f0f6fc !important;
}

.code-editor-code .hljs-keyword,
.code-editor-code .hljs-selector-tag,
.code-editor-code .hljs-literal,
.code-editor-code .hljs-title,
.code-editor-code .hljs-section,
.code-editor-code .hljs-type,
.code-editor-code .hljs-name {
  color: #ff7b72 !important;
}

.code-editor-code .hljs-string,
.code-editor-code .hljs-template-literal {
  color: #a5d6ff !important;
}

.code-editor-code .hljs-comment {
  color: #8b949e !important;
  font-style: italic;
}

.code-editor-code .hljs-number {
  color: #79c0ff !important;
}

.code-editor-code .hljs-built_in,
.code-editor-code .hljs-builtin-name {
  color: #7ee787 !important;
}

.code-editor-code .hljs-variable,
.code-editor-code .hljs-template-variable {
  color: #ffa657 !important;
}

.code-editor-code .hljs-function,
.code-editor-code .hljs-class {
  color: #d2a8ff !important;
}

.code-editor-code .hljs-attr,
.code-editor-code .hljs-attribute {
  color: #79c0ff !important;
}

.code-editor-code .hljs-tag {
  color: #7ee787 !important;
}

.code-editor-code .hljs-doctag {
  color: #f2cc60 !important;
}

.code-editor-code .hljs-meta {
  color: #d2a8ff !important;
}

/* Additional highlight.js styles for better coverage */
.code-editor-code .hljs-strong {
  color: #f0f6fc !important;
  font-weight: bold;
}

.code-editor-code .hljs-code {
  color: #a5d6ff !important;
  padding: 2px 4px;
  border-radius: 3px;
}

.code-editor-code .hljs-formula {
  color: #7ee787 !important;
  padding: 2px 4px;
  border-radius: 3px;
}

.code-editor-code .hljs-emphasis {
  color: #f0f6fc !important;
  font-style: italic;
}

.code-editor-code .hljs-quote {
  color: #8b949e !important;
  font-style: italic;
}

.code-editor-code .hljs-bullet {
  color: #ff7b72 !important;
}

.code-editor-code .hljs-subst {
  color: #f0f6fc !important;
}

.code-editor-code .hljs-regexp {
  color: #7ee787 !important;
}

.code-editor-code .hljs-symbol {
  color: #79c0ff !important;
}

.code-editor-code .hljs-deletion {
  color: #ffa198 !important;
  background: rgba(248, 81, 73, 0.15);
}

.code-editor-code .hljs-addition {
  color: #56d364 !important;
  background: rgba(46, 160, 67, 0.15);
}
