{"name": "@agent-tars/core", "version": "0.3.0-beta.1", "description": "Agent TARS core.", "main": "dist/index.js", "types": "dist/index.d.ts", "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org"}, "files": ["dist"], "scripts": {"dev": "rslib build --watch", "build": "rslib build", "prepublishOnly": "pnpm run build", "test": "vitest run", "test:watch": "vitest", "agent:snapshot:genreate": "npx tsx snapshot/runner.ts generate all", "agent:snapshot:test": "npx vitest snapshot/index.test.ts", "benchmark:crawl": "cd benchmark/crawl && pnpm start"}, "dependencies": {"imagemin-mozjpeg": "^10.0.0", "imagemin-webp": "^8.0.0"}, "devDependencies": {"@mcp-agent/core": "workspace:*", "@agent-tars/interface": "workspace:*", "@gui-agent/operator-browser": "workspace:*", "@agent-infra/mcp-server-browser": "1.1.10", "@agent-infra/mcp-server-commands": "1.1.10", "@agent-infra/mcp-server-filesystem": "1.1.10", "@agent-infra/search": "0.0.5", "@agent-infra/browser": "0.1.1", "@agent-infra/browser-search": "0.0.5", "@agent-infra/shared": "0.0.2", "imagemin-pngquant": "^9.0.2", "imagemin": "^8.0.1", "@multimodal/agent": "workspace:*", "@multimodal/agent-snapshot": "workspace:*", "@types/imagemin": "^8.0.5", "@rslib/core": "0.10.0", "@types/node": "22.15.30", "typescript": "^5.5.3", "vitest": "3.2.4"}}