/**
 * Agent TARS 配置文件
 * 配置 Doubao 1.5 Thinking Vision Pro 模型
 */

module.exports = {
  // 模型配置
  model: {
    provider: 'volcengine',
    id: 'ep-20250730231730-swwgj', // 你的模型端点ID
    apiKey: 'aa3fac83-29e2-4f45-a3ca-489a77c1534c', // 你的API密钥
    baseURL: 'https://ark.cn-beijing.volces.com/api/v3', // 火山引擎API基础URL
  },

  // 工作空间配置
  workspace: {
    workingDirectory: process.cwd(),
  },

  // 浏览器配置
  browser: {
    type: 'local',
    headless: false, // 设置为true可以无头模式运行
    control: 'hybrid', // 混合控制模式，结合DOM和视觉识别
  },

  // 搜索配置
  search: {
    provider: 'browser_search',
    count: 10,
    browserSearch: {
      engine: 'google',
      needVisitedUrls: false,
    },
  },

  // MCP服务器配置
  mcpImpl: 'in-memory',
  mcpServers: {},

  // 执行配置
  maxIterations: 100,
  maxTokens: 8192,
  enableStreamingToolCallEvents: true,

  // 实验性功能
  experimental: {
    dumpMessageHistory: false, // 设置为true可以导出消息历史
  },

  // 规划器配置（可选）
  planner: {
    enable: false,
    maxSteps: 3,
  },

  // 服务器配置（如果使用Web UI）
  server: {
    port: 3000,
    host: 'localhost',
  },

  // 日志配置
  logLevel: 'info', // 可选: 'debug', 'info', 'warn', 'error'
};
