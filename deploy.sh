#!/bin/bash

# UI-TARS Agent 服务器部署脚本

set -e

echo "🚀 开始部署 UI-TARS Agent 服务器..."

# 检查 Docker 是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

# 创建必要的目录
echo "📁 创建工作目录..."
mkdir -p workspace logs ssl

# 设置权限
chmod 755 workspace logs

# 构建并启动服务
echo "🔨 构建 Docker 镜像..."
docker-compose build

echo "🚀 启动服务..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 健康检查
echo "🔍 检查服务状态..."
if curl -f http://localhost:3000/health > /dev/null 2>&1; then
    echo "✅ 服务启动成功！"
    echo "🌐 API 地址: http://localhost:3000"
    echo "📊 服务状态: http://localhost:3000/health"
else
    echo "❌ 服务启动失败，请检查日志:"
    docker-compose logs ui-tars-agent
    exit 1
fi

echo "📋 部署完成！"
echo ""
echo "常用命令:"
echo "  查看日志: docker-compose logs -f ui-tars-agent"
echo "  停止服务: docker-compose down"
echo "  重启服务: docker-compose restart"
echo "  更新服务: docker-compose pull && docker-compose up -d"
