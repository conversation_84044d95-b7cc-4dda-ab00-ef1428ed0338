{"provider": "volcengine", "request": {"provider": "volcengine", "model": "ep-20250510145437-5sxhs", "temperature": 0.3, "messages": [{"role": "system", "content": "你是一个专业的研究报告生成器。根据提供的所有信息，生成一份全面、详细且结构清晰的研究报告。报告应该包含详细的分析、洞见，并引用所有相关的事实和数据。使用专业的语言和格式，包括标题、小标题、要点和总结。确保报告全面覆盖了所有已收集的重要信息。"}, {"role": "user", "content": "用户的原始查询是：帮我调研一下 ByteDance 的开源项目，给出一份完整的报告\n\n我期待覆盖的信息： \n1. 主要的开源项目、贡献者；\n2. 应用场景； \n\n\n要求报告输出中文。\n\n以下是我们收集到的所有信息：\n\n来自工具 web-search 的结果:\n{\n  \"query\": \"ByteDance GitHub 开源项目 主要项目 贡献者\",\n  \"engine\": \"google\",\n  \"totalResults\": 3,\n  \"results\": [\n    {\n      \"index\": 1,\n      \"title\": \"字节跳动在GitHub上有哪些开源项目_github 字节跳动-CSDN博客\",\n      \"url\": \"https://blog.csdn.net/interpromotion/article/details/147456150\",\n      \"content\": \"[程序员差不多先生](https://blog.csdn.net/interpromotion \\\"程序员差不多先生\\\") 于 2025-04-23 15:44:05 发布\\n\\n版权声明：本文为博主原创文章，遵循 [CC 4.0 BY-SA](http://creativecommons.org/licenses/by-sa/4.0/) 版权协议，转载请附上原文出处链接和本声明。\\n\\n字节跳动（ByteDance）在GitHub上开源了许多项目，涵盖前端、后端、云原生、AI、数据库等多个领域。以下是一些典型项目及其简介：\\n\\n* * *\\n\\n#### **1\\\\. 前端 & 跨平台开发**\\n\\n*   **Hippy**\\n    \\n    *   **仓库**: [Tencent/Hippy](https://github.com/Tencent/Hippy)（注：Hippy 最初由腾讯开源，但字节跳动内部有深度定制和扩展版本）\\n    *   **简介**: 高性能跨端开发框架，支持 React/Vue 语法，广泛用于抖音、今日头条等 App 内嵌页面。\\n*   **IconPark**\\n    \\n    *   **仓库**: [bytedance/IconPark](https://github.com/bytedance/IconPark)\\n    *   **简介**: 开源图标库，提供超过 2,000 个高质量图标，支持按需定制颜色、大小、线条风格等。\\n*   **Semi Design**\\n    \\n    *   **仓库**: [DouyinFE/semi-design](https://github.com/DouyinFE/semi-design)\\n    *   **简介**: 抖音前端团队开源的现代企业级 UI 设计系统，支持 React 组件库与设计工具集成。\\n\\n* * *\\n\\n#### **2\\\\. 云原生 & 微服务**\\n\\n*   **CloudWeGo**\\n    \\n    *   **仓库**: [cloudwego](https://github.com/cloudwego)\\n    *   **简介**: 字节跳动开源的云原生微服务生态，核心项目包括：\\n        *   **Kitex**: 高性能 RPC 框架（Golang）。\\n    ...(content trimmed)\"\n    },\n    {\n      \"index\": 2,\n      \"title\": \"国内互联网大厂开源github开源项目主页和开源贡献排名粗略统计，按照开源贡献度排名，目前国内阿里的开源项目最多，应用面 - 掘金\",\n      \"url\": \"https://juejin.cn/post/7165837179200110629\",\n      \"content\": \"**中国开源项目 Top 20 阿里系占 6 席** 尽管大多开源项目还是以国际公司为主导推出，但中国开源势力已经开始登上国际化的舞台。报告根据 GitHub Event 的数据，汇总出 \\\"中国开源项目 Top20\\\"（注：由于本轮发布项目排行数据主要参考 GitHub 中 Event 数据，目前在 GitHub中没有数据的项目尚未计算在内，例如 OpenHarmony、openEuler、MindSpore 等）。\"\n    },\n    {\n      \"index\": 3,\n      \"title\": \"国内互联网大厂开源github开源项目主页和开源贡献排名\",\n      \"url\": \"https://www.163.com/dy/article/HM60SVCQ05383QFT.html\",\n      \"content\": \"2022-11-14 23:36:47　来源: [JEECG低代码平台](https://www.163.com/dy/media/T1538209436228.html) 北京  [举报](https://www.163.com/special/0077jt/tipoff.html?title=%E5%9B%BD%E5%86%85%E4%BA%92%E8%81%94%E7%BD%91%E5%A4%A7%E5%8E%82%E5%BC%80%E6%BA%90github%E5%BC%80%E6%BA%90%E9%A1%B9%E7%9B%AE%E4%B8%BB%E9%A1%B5%E5%92%8C%E5%BC%80%E6%BA%90%E8%B4%A1%E7%8C%AE%E6%8E%92%E5%90%8D)\\n\\n> 粗略统计，按照开源贡献度排名，目前国内阿里的开源项目最多，应用面也最广，百度echart也非常棒  \\n\\n##### 阿里巴巴\\n\\nhttps://github.com/alibaba\\n\\nhttps://github.com/ant-design\\n\\nhttps://github.com/apache/dubbo\\n\\n##### 百度\\n\\nhttps://github.com/baidu https://github.com/apache/echarts https://github.com/ecomfe https://github.com/fex-team\\n\\n##### 腾讯\\n\\nhttps://github.com/Tencent\\n\\n##### 饿了么前端\\n\\nhttps://github.com/ElemeFE\\n\\n##### 美团\\n\\nhttps://github.com/Meituan-Dianping\\n\\n##### 字节跳动\\n\\nhttps://github.com/bytedance\\n\\n##### 有赞\\n\\nhttps://github.com/youzan\\n\\n##### 当当网\\n\\nhttps://github.com/dangdangdotcom\\n\\n##### 360\\n\\nhttps://github.com/Qihoo360\\n\\n##### 新浪\\n\\nhttps://github.com/weibocom\\n\\n##### 京东\\n\\nhttps://github.com/JDF...(content trimmed)\"\n    }\n  ]\n}\n\n\n来自工具 visit-link 的结果:\n{\n  \"title\": \"Bytedance Inc.\",\n  \"url\": \"https://github.com/bytedance\",\n  \"excerpt\": \"Bytedance Inc. has 344 repositories available. Follow their code on GitHub.\",\n  \"content\": \"[Skip to content](#start-of-content)\\n\\n## Navigation Menu\\n\\n*   *   [\\n        \\n        GitHub Copilot\\n        \\n        Write better code with AI\\n        \\n        \\n        \\n        ](https://github.com/features/copilot)\\n    *   [\\n        \\n        GitHub Models New\\n        \\n        Manage and compare prompts\\n        \\n        \\n        \\n        ](https://github.com/features/models)\\n    *   [\\n        \\n        GitHub Advanced Security\\n        \\n        Find and fix vulnerabilities\\n        \\n        \\n        \\n        ](https://github.com/security/advanced-security)\\n    *   [\\n        \\n        Actions\\n        \\n        Automate any workflow\\n        \\n        \\n        \\n        ](https://github.com/features/actions)\\n    *   [\\n        \\n        Codespaces\\n        \\n        Instant dev environments\\n        \\n        \\n        \\n        ](https://github.com/features/codespaces)\\n    \\n    *   [\\n        \\n        Issues\\n        \\n        Plan and track work\\n        \\n        \\n        \\n        ](https://github.com/features/issues)\\n    *   [\\n        \\n        Code Review\\n        \\n        Manage code changes\\n        \\n        \\n        \\n        ](https://github.com/features/code-review)\\n    *   [\\n        \\n        Discussions\\n        \\n        Collaborate outside of code\\n        \\n        \\n        \\n        ](https://github.com/features/discussions)\\n    *   [\\n        \\n        Code Search\\n        \\n        Find more, search less\\n        \\n        \\n        \\n        ](https://github.com/features/code-search)\\n    \\n\\n*   Explore\\n    \\n    *   [Learning Pathways](https://resources.github.com/learn/pathways)\\n    *   [Events & Webinars](https://resources.github.com/)\\n    *   [Ebooks & Whitepapers](https://github.com/resources/whitepapers)\\n    *   [Customer Stories](https://github.com/customer-stories)\\n    *   [Partners](https://partner.github.com/)\\n    *   [Executive Insights](https://github.com/solutions/executive-insights)\\n    \\n*   *   [\\n        \\n        GitHub Sponsors\\n        \\n        Fund open source developers\\n        \\n        \\n        \\n        ](https://github.com/sponsors)\\n    \\n    *   [\\n        \\n        The ReadME Project\\n        \\n        GitHub community articles\\n        \\n        \\n        \\n        ](https://github.com/readme)\\n    \\n*   *   [\\n        \\n        Enterprise platform\\n        \\n        AI-powered developer platform\\n        \\n        \\n        \\n        ](https://github.com/enterprise)\\n    \\n*   [Pricing](https://github.com/pricing)\\n\\n## Provide feedback\\n\\n## Saved searches\\n\\n## Use saved searches to filter your results more quickly\\n\\n[Sign up](https://github.com/signup?ref_cta=Sign+up&ref_loc=header+logged+out&ref_page=%2F%3Corg-login%3E&source=header)\\n\\n![@bytedance](https://avatars.githubusercontent.com/u/4158466?s=200&v=4)\\n\\n*   [Overview](https://github.com/bytedance)\\n*   [Repositories 344](https://github.com/orgs/bytedance/repositories)\\n*   [Projects 2](https://github.com/orgs/bytedance/projects)\\n*   [Packages](https://github.com/orgs/bytedance/packages)\\n*   [People 73](https://github.com/orgs/bytedance/people)\\n\\n## Pinned Loading\\n\\n1.  A blazingly fast JSON serializing & deserializing library\\n    \\n    Go [8.1k](https://github.com/bytedance/sonic/stargazers) [378](https://github.com/bytedance/sonic/forks)\\n    \\n2.  A GUI Agent application based on UI-TARS(Vision-Language Model) that allows you to control your computer using natural language.\\n    \\n    TypeScript [14.2k](https://github.com/bytedance/UI-TARS-desktop/stargazers) [1.2k](https://github.com/bytedance/UI-TARS-desktop/forks)\\n    \\n3.  Taming Stable Diffusion for Lip Sync!\\n    \\n    Python [4.1k](https://github.com/bytedance/LatentSync/stargazers) [615](https://github.com/bytedance/LatentSync/forks)\\n    \\n4.  DeerFlow is a community-driven Deep Research framework, combining language models with tools like web search, crawling, and Python execution, while contributing back to the open-source community.\\n    \\n    TypeScript [11.8k](https://github.com/bytedance/deer-flow/stargazers) [1.2k](https://github.com/bytedance/deer-flow/forks)\\n    \\n\\n### Repositories\\n\\nType\\n\\nSelect type\\n\\nAll Public Sources Forks Archived Mirrors Templates\\n\\nLanguage\\n\\nSelect language\\n\\nAll C C++ Cuda Dart Go HTML Java JavaScript Jupyter Notebook Kotlin MLIR Objective-C Perl PHP PureBasic Python Ruby Rust Shell Solidity Swift TypeScript\\n\\nSort\\n\\nSelect order\\n\\nLast updated Name Stars\\n\\nShowing 10 of 344 repositories\\n\\n*   [gg](https://github.com/bytedance/gg) Public\\n    \\n    🔥gg is a basic library of generics for Go language developed by ByteDance. It is based on the Go 1.18+ generic features and provides efficient, type-safe and rich generic data structures and tool functions.\\n    \\n    Go\\n    \\n    [81](https://github.com/bytedance/gg/stargazers)\\n    \\n    Apache-2.0\\n    \\n    [7](https://github.com/bytedance/gg/forks) [0](https://github.com/bytedance/gg/issues) [2](https://github.com/bytedance/gg/pulls)\\n    \\n    Updated May 25, 2025\\n    \\n*   [UI-TARS-desktop](https://github.com/bytedance/UI-TARS-desktop) Public\\n    \\n    A GUI Agent application based on UI-TARS(Vision-Language Model) that allows you to control your computer using natural language.\\n    \\n*   Python\\n    \\n    [140](https://github.com/bytedance/ComfyUI_InfiniteYou/stargazers)\\n    \\n    Apache-2.0\\n    \\n    [26](https://github.com/bytedance/ComfyUI_InfiniteYou/forks) [9](https://github.com/bytedance/ComfyUI_InfiniteYou/issues) [0](https://github.com/bytedance/ComfyUI_InfiniteYou/pulls)\\n    \\n    Updated May 23, 2025\\n    \\n*   [flowgram.ai](https://github.com/bytedance/flowgram.ai) Public\\n    \\n    FlowGram is a node-based flow building engine that helps developers quickly create workflows in either fixed layout or free connection layout modes\\n    \\n*   [SmartInfra](https://github.com/bytedance/SmartInfra) Public\\n    \\n    A smart, efficient and powerful automated configuration platform\\n    \\n    HTML\\n    \\n    [1](https://github.com/bytedance/SmartInfra/stargazers)\\n    \\n    Apache-2.0 0\\n    \\n    [0](https://github.com/bytedance/SmartInfra/issues) [0](https://github.com/bytedance/SmartInfra/pulls)\\n    \\n    Updated May 23, 2025\\n    \\n*   [Dolphin](https://github.com/bytedance/Dolphin) Public\\n    \\n    The official repo for “Dolphin: Document Image Parsing via Heterogeneous Anchor Prompting”, ACL, 2025.\\n    \\n    Python\\n    \\n    [527](https://github.com/bytedance/Dolphin/stargazers)\\n    \\n    MIT\\n    \\n    [30](https://github.com/bytedance/Dolphin/forks) [28](https://github.com/bytedance/Dolphin/issues) [0](https://github.com/bytedance/Dolphin/pulls)\\n    \\n    Updated May 23, 2025\\n    \\n*   [web-bench](https://github.com/bytedance/web-bench) Public\\n    \\n    Web-Bench is a benchmark designed to evaluate the performance of LLMs in actual Web development.\\n    \\n    JavaScript\\n    \\n    [85](https://github.com/bytedance/web-bench/stargazers)\\n    \\n    Apache-2.0\\n    \\n    [5](https://github.com/bytedance/web-bench/forks) [6](https://github.com/bytedance/web-bench/issues) [0](https://github.com/bytedance/web-bench/pulls)\\n    \\n    Updated May 23, 2025\\n    \\n*   [g3](https://github.com/bytedance/g3) Public\\n    \\n    Enterprise-oriented Generic Proxy Solutions\\n    \\n*   [vArmor](https://github.com/bytedance/vArmor) Public\\n    \\n    vArmor is a cloud native container sandbox system based on AppArmor/BPF/Seccomp. It also includes multiple built-in protection rules that are ready to use out of the box.\\n    \\n    Go\\n    \\n    [368](https://github.com/bytedance/vArmor/stargazers)\\n    \\n    Apache-2.0\\n    \\n    [39](https://github.com/bytedance/vArmor/forks) [2](https://github.com/bytedance/vArmor/issues) [1](https://github.com/bytedance/vArmor/pulls)\\n    \\n    Updated May 23, 2025\\n    \\n*   Java\\n    \\n    [55](https://github.com/bytedance/CompoundVM/stargazers)\\n    \\n    GPL-2.0\\n    \\n    [2](https://github.com/bytedance/CompoundVM/forks) [5](https://github.com/bytedance/CompoundVM/issues) [2](https://github.com/bytedance/CompoundVM/pulls)\\n    \\n    Updated May 23, 2025\\n    \\n\\n#### Most used topics\"\n}\n\n\n来自工具 web-search 的结果:\n{\n  \"query\": \"ByteDance 主要开源项目 应用场景\",\n  \"engine\": \"google\",\n  \"totalResults\": 3,\n  \"results\": [\n    {\n      \"index\": 1,\n      \"title\": \"字节跳动开源官网\",\n      \"url\": \"https://opensource.bytedance.com/project\",\n      \"content\": \"\"\n    },\n    {\n      \"index\": 2,\n      \"title\": \"\",\n      \"url\": \"https://zhuanlan.zhihu.com/p/546857592\",\n      \"content\": \"知乎，让每一次点击都充满意义 —— 欢迎来到知乎，发现问题背后的世界。\"\n    },\n    {\n      \"index\": 3,\n      \"title\": \"字节跳动在GitHub上有哪些开源项目_github 字节跳动-CSDN博客\",\n      \"url\": \"https://blog.csdn.net/interpromotion/article/details/147456150\",\n      \"content\": \"[程序员差不多先生](https://blog.csdn.net/interpromotion \\\"程序员差不多先生\\\") 于 2025-04-23 15:44:05 发布\\n\\n版权声明：本文为博主原创文章，遵循 [CC 4.0 BY-SA](http://creativecommons.org/licenses/by-sa/4.0/) 版权协议，转载请附上原文出处链接和本声明。\\n\\n字节跳动（ByteDance）在GitHub上开源了许多项目，涵盖前端、后端、云原生、AI、数据库等多个领域。以下是一些典型项目及其简介：\\n\\n* * *\\n\\n#### **1\\\\. 前端 & 跨平台开发**\\n\\n*   **Hippy**\\n    \\n    *   **仓库**: [Tencent/Hippy](https://github.com/Tencent/Hippy)（注：Hippy 最初由腾讯开源，但字节跳动内部有深度定制和扩展版本）\\n    *   **简介**: 高性能跨端开发框架，支持 React/Vue 语法，广泛用于抖音、今日头条等 App 内嵌页面。\\n*   **IconPark**\\n    \\n    *   **仓库**: [bytedance/IconPark](https://github.com/bytedance/IconPark)\\n    *   **简介**: 开源图标库，提供超过 2,000 个高质量图标，支持按需定制颜色、大小、线条风格等。\\n*   **Semi Design**\\n    \\n    *   **仓库**: [DouyinFE/semi-design](https://github.com/DouyinFE/semi-design)\\n    *   **简介**: 抖音前端团队开源的现代企业级 UI 设计系统，支持 React 组件库与设计工具集成。\\n\\n* * *\\n\\n#### **2\\\\. 云原生 & 微服务**\\n\\n*   **CloudWeGo**\\n    \\n    *   **仓库**: [cloudwego](https://github.com/cloudwego)\\n    *   **简介**: 字节跳动开源的云原生微服务生态，核心项目包括：\\n        *   **Kitex**: 高性能 RPC 框架（Golang）。\\n    ...(content trimmed)\"\n    }\n  ]\n}\n\n\n来自工具 web-search 的结果:\n{\n  \"query\": \"ByteDance 开源项目 贡献者 背景信息\",\n  \"engine\": \"google\",\n  \"totalResults\": 3,\n  \"results\": [\n    {\n      \"index\": 1,\n      \"title\": \"字节跳动开源官网\",\n      \"url\": \"https://opensource.bytedance.com/\",\n      \"content\": \"\"\n    },\n    {\n      \"index\": 2,\n      \"title\": \"Docs\",\n      \"url\": \"https://docs.feishu.cn/v/wiki/CaHIw9Cjhi6b9okTrdqcEJBQn6g/a7\",\n      \"content\": \"字节跳动自 2012 年成立伊始，就将数据挖掘和推荐引擎技术作为公司产品的核心，推出基于智能推荐算法的资讯产品「今日头条」，它是国内最早把人工智能技术运用到移动应用场景中的个性化内容推荐产品之一。​\\n\\n梁汝波在年会中提到，字节跳动在2023年才开始布局AI，“已经是太晚了”。根据界面报道，去年一年，字节跳动创始人张一鸣将主要精力都花在了AI上。2024年2月初，张楠宣布卸任抖音集团CEO的，转战AI生成式视频领域的剪映。这无一不显示，字节将迅速回归AI的主业。​\\n\\n过去这半年，字节在 AI 应用尤其是大模型赛道火力全开。内部完成了组织架构调整，全面押注 AI 原生应用，连续推出了十多款基于 AI 和大模型的产品，让整个公司快速升级为 “超级 AI 应用工厂”。目前已经初见成效，占领应用场景的目标非常明确。​\\n\\n本文完整梳理了字节AI团队架构和发展沿革，字节在AI大模型和产品上的布局和数据表现，同时也推测了字节可能上线的新产品。Enjoy！​\\n\\n如果想获取全部字节AI相关资料，关注公众号并发送“字节AI”和本文朋友圈分享截图即可获取​\\n\\n2023年的1月，字节跳动组建了首个专注于大型模型研发的团队，该团队由其搜索部门主导，朱文佳担任负责人。朱文佳此前在TikTok担任技术负责人，并在加入字节跳动之前在百度担任搜索部主任架构师。2019年，朱文佳曾担任今日头条的CEO，后于2021年转任TikTok产品技术负责人。​\\n\\n该团队由两个小组构成，一个小组来自字节跳动的原搜索部门，负责开发语言模型；另一个小组属于产品研发与工程架构部下的智能创作团队，负责图片模型的开发。陆游，抖音的社交负责人，也在2023年上半年加入了这个大型模型团队。​\"\n    },\n    {\n      \"index\": 3,\n      \"title\": \"字节跳动开源内部微服务中间件 CloudWeGo\",\n      \"url\": \"https://www.cloudwego.io/zh/blog/2021/09/07/%E5%AD%97%E8%8A%82%E8%B7%B3%E5%8A%A8%E5%BC%80%E6%BA%90%E5%86%85%E9%83%A8%E5%BE%AE%E6%9C%8D%E5%8A%A1%E4%B8%AD%E9%97%B4%E4%BB%B6-cloudwego/\",\n      \"content\": \"字节跳动宣布正式开源内部微服务中间件 CloudWeGo\\n\\n## 开源背景\\n\\n[CloudWeGo](https://github.com/cloudwego) 是一套由字节跳动开源的、以 Go 语言为核心的、可快速构建企业级云原生架构的中间件集合，专注于微服务通信与治理，具备高性能、可扩展、高可靠的特点。\\n\\n字节跳动内部使用 Golang 作为主要的业务开发语言，我们支持着数万个 Golang 微服务的可靠通信，经过数量众多的微服务和海量流量的验证，我们已经有了较为成熟的微服务最佳实践，于是考虑将内部的实践开源出去丰富社区生态。 但微服务相关的项目较多，每个项目单独开源对外部用户并不友好，为了更好地让大家聚焦于微服务，我们以 CloudWeGo 作为项目名，逐步将内部微服务体系的项目开源，内外统一使用开源库，各项目以开源库为主进行迭代。\\n\\n内外维护一套代码，统一迭代演进，是我们开源前明确的原则，但毕竟涉及到代码库的调整，我们要保证内部用户尽可能无感知的迁移到开源库，本着对内部和开源用户负责的态度，我们要先确认内部可以平滑过渡，所以开源时并未对外宣传。 让我们欣慰的是，在未宣传的情况下，一个月内 [Kitex](https://github.com/cloudwego/kitex) 收获了 1.2k stars，[Netpoll](https://github.com/cloudwego/netpoll) 收获了 700+ stars。\\n\\nCloudWeGo 不仅仅是一个对外的开源项目，也是一个真实的超大规模企业级实践项目。\\n\\n我们希望通过 CloudWeGo 丰富云原生社区的 Golang 产品体系，助力其他企业快速构建云原生架构，也希望吸引外部开发者共建，促进面向多元场景支持的演进，丰富产品能力。\\n\\n因为 CloudWeGo 下的项目会依赖很多内部的基础工具库，我们也推动将内部常用的 Golang 基础工具库开源出去，统一在 [bytedance/gopkg](https://github.com/bytedance/gopkg) 维护。\\n\\n## CloudWeGo 开源项目\\n\\nCloudWeGo 第一批以 [Kitex](https://github.com/cloudwego/kitex) RPC 框架和 [Netpoll](https://github.c...(content trimmed)\"\n    }\n  ]\n}\n\n\n来自工具 web-search 的结果:\n{\n  \"query\": \"ByteDance 开源项目 贡献者 背景信息\",\n  \"engine\": \"google\",\n  \"totalResults\": 3,\n  \"results\": [\n    {\n      \"index\": 1,\n      \"title\": \"字节跳动开源官网\",\n      \"url\": \"https://opensource.bytedance.com/\",\n      \"content\": \"\"\n    },\n    {\n      \"index\": 2,\n      \"title\": \"Docs\",\n      \"url\": \"https://docs.feishu.cn/v/wiki/CaHIw9Cjhi6b9okTrdqcEJBQn6g/a7\",\n      \"content\": \"字节跳动自 2012 年成立伊始，就将数据挖掘和推荐引擎技术作为公司产品的核心，推出基于智能推荐算法的资讯产品「今日头条」，它是国内最早把人工智能技术运用到移动应用场景中的个性化内容推荐产品之一。​\\n\\n梁汝波在年会中提到，字节跳动在2023年才开始布局AI，“已经是太晚了”。根据界面报道，去年一年，字节跳动创始人张一鸣将主要精力都花在了AI上。2024年2月初，张楠宣布卸任抖音集团CEO的，转战AI生成式视频领域的剪映。这无一不显示，字节将迅速回归AI的主业。​\\n\\n过去这半年，字节在 AI 应用尤其是大模型赛道火力全开。内部完成了组织架构调整，全面押注 AI 原生应用，连续推出了十多款基于 AI 和大模型的产品，让整个公司快速升级为 “超级 AI 应用工厂”。目前已经初见成效，占领应用场景的目标非常明确。​\\n\\n本文完整梳理了字节AI团队架构和发展沿革，字节在AI大模型和产品上的布局和数据表现，同时也推测了字节可能上线的新产品。Enjoy！​\\n\\n如果想获取全部字节AI相关资料，关注公众号并发送“字节AI”和本文朋友圈分享截图即可获取​\\n\\n2023年的1月，字节跳动组建了首个专注于大型模型研发的团队，该团队由其搜索部门主导，朱文佳担任负责人。朱文佳此前在TikTok担任技术负责人，并在加入字节跳动之前在百度担任搜索部主任架构师。2019年，朱文佳曾担任今日头条的CEO，后于2021年转任TikTok产品技术负责人。​\\n\\n该团队由两个小组构成，一个小组来自字节跳动的原搜索部门，负责开发语言模型；另一个小组属于产品研发与工程架构部下的智能创作团队，负责图片模型的开发。陆游，抖音的社交负责人，也在2023年上半年加入了这个大型模型团队。​\"\n    },\n    {\n      \"index\": 3,\n      \"title\": \"字节跳动开源内部微服务中间件 CloudWeGo\",\n      \"url\": \"https://www.cloudwego.io/zh/blog/2021/09/07/%E5%AD%97%E8%8A%82%E8%B7%B3%E5%8A%A8%E5%BC%80%E6%BA%90%E5%86%85%E9%83%A8%E5%BE%AE%E6%9C%8D%E5%8A%A1%E4%B8%AD%E9%97%B4%E4%BB%B6-cloudwego/\",\n      \"content\": \"字节跳动宣布正式开源内部微服务中间件 CloudWeGo\\n\\n## 开源背景\\n\\n[CloudWeGo](https://github.com/cloudwego) 是一套由字节跳动开源的、以 Go 语言为核心的、可快速构建企业级云原生架构的中间件集合，专注于微服务通信与治理，具备高性能、可扩展、高可靠的特点。\\n\\n字节跳动内部使用 Golang 作为主要的业务开发语言，我们支持着数万个 Golang 微服务的可靠通信，经过数量众多的微服务和海量流量的验证，我们已经有了较为成熟的微服务最佳实践，于是考虑将内部的实践开源出去丰富社区生态。 但微服务相关的项目较多，每个项目单独开源对外部用户并不友好，为了更好地让大家聚焦于微服务，我们以 CloudWeGo 作为项目名，逐步将内部微服务体系的项目开源，内外统一使用开源库，各项目以开源库为主进行迭代。\\n\\n内外维护一套代码，统一迭代演进，是我们开源前明确的原则，但毕竟涉及到代码库的调整，我们要保证内部用户尽可能无感知的迁移到开源库，本着对内部和开源用户负责的态度，我们要先确认内部可以平滑过渡，所以开源时并未对外宣传。 让我们欣慰的是，在未宣传的情况下，一个月内 [Kitex](https://github.com/cloudwego/kitex) 收获了 1.2k stars，[Netpoll](https://github.com/cloudwego/netpoll) 收获了 700+ stars。\\n\\nCloudWeGo 不仅仅是一个对外的开源项目，也是一个真实的超大规模企业级实践项目。\\n\\n我们希望通过 CloudWeGo 丰富云原生社区的 Golang 产品体系，助力其他企业快速构建云原生架构，也希望吸引外部开发者共建，促进面向多元场景支持的演进，丰富产品能力。\\n\\n因为 CloudWeGo 下的项目会依赖很多内部的基础工具库，我们也推动将内部常用的 Golang 基础工具库开源出去，统一在 [bytedance/gopkg](https://github.com/bytedance/gopkg) 维护。\\n\\n## CloudWeGo 开源项目\\n\\nCloudWeGo 第一批以 [Kitex](https://github.com/cloudwego/kitex) RPC 框架和 [Netpoll](https://github.c...(content trimmed)\"\n    }\n  ]\n}\n\n\n来自工具 web-search 的结果:\n{\n  \"query\": \"ByteDance 开源项目 贡献者 背景信息\",\n  \"engine\": \"google\",\n  \"totalResults\": 3,\n  \"results\": [\n    {\n      \"index\": 1,\n      \"title\": \"字节跳动开源官网\",\n      \"url\": \"https://opensource.bytedance.com/\",\n      \"content\": \"\"\n    },\n    {\n      \"index\": 2,\n      \"title\": \"Docs\",\n      \"url\": \"https://docs.feishu.cn/v/wiki/CaHIw9Cjhi6b9okTrdqcEJBQn6g/a7\",\n      \"content\": \"字节跳动自 2012 年成立伊始，就将数据挖掘和推荐引擎技术作为公司产品的核心，推出基于智能推荐算法的资讯产品「今日头条」，它是国内最早把人工智能技术运用到移动应用场景中的个性化内容推荐产品之一。​\\n\\n梁汝波在年会中提到，字节跳动在2023年才开始布局AI，“已经是太晚了”。根据界面报道，去年一年，字节跳动创始人张一鸣将主要精力都花在了AI上。2024年2月初，张楠宣布卸任抖音集团CEO的，转战AI生成式视频领域的剪映。这无一不显示，字节将迅速回归AI的主业。​\\n\\n过去这半年，字节在 AI 应用尤其是大模型赛道火力全开。内部完成了组织架构调整，全面押注 AI 原生应用，连续推出了十多款基于 AI 和大模型的产品，让整个公司快速升级为 “超级 AI 应用工厂”。目前已经初见成效，占领应用场景的目标非常明确。​\\n\\n本文完整梳理了字节AI团队架构和发展沿革，字节在AI大模型和产品上的布局和数据表现，同时也推测了字节可能上线的新产品。Enjoy！​\\n\\n如果想获取全部字节AI相关资料，关注公众号并发送“字节AI”和本文朋友圈分享截图即可获取​\\n\\n2023年的1月，字节跳动组建了首个专注于大型模型研发的团队，该团队由其搜索部门主导，朱文佳担任负责人。朱文佳此前在TikTok担任技术负责人，并在加入字节跳动之前在百度担任搜索部主任架构师。2019年，朱文佳曾担任今日头条的CEO，后于2021年转任TikTok产品技术负责人。​\\n\\n该团队由两个小组构成，一个小组来自字节跳动的原搜索部门，负责开发语言模型；另一个小组属于产品研发与工程架构部下的智能创作团队，负责图片模型的开发。陆游，抖音的社交负责人，也在2023年上半年加入了这个大型模型团队。​\"\n    },\n    {\n      \"index\": 3,\n      \"title\": \"字节跳动开源内部微服务中间件 CloudWeGo\",\n      \"url\": \"https://www.cloudwego.io/zh/blog/2021/09/07/%E5%AD%97%E8%8A%82%E8%B7%B3%E5%8A%A8%E5%BC%80%E6%BA%90%E5%86%85%E9%83%A8%E5%BE%AE%E6%9C%8D%E5%8A%A1%E4%B8%AD%E9%97%B4%E4%BB%B6-cloudwego/\",\n      \"content\": \"字节跳动宣布正式开源内部微服务中间件 CloudWeGo\\n\\n## 开源背景\\n\\n[CloudWeGo](https://github.com/cloudwego) 是一套由字节跳动开源的、以 Go 语言为核心的、可快速构建企业级云原生架构的中间件集合，专注于微服务通信与治理，具备高性能、可扩展、高可靠的特点。\\n\\n字节跳动内部使用 Golang 作为主要的业务开发语言，我们支持着数万个 Golang 微服务的可靠通信，经过数量众多的微服务和海量流量的验证，我们已经有了较为成熟的微服务最佳实践，于是考虑将内部的实践开源出去丰富社区生态。 但微服务相关的项目较多，每个项目单独开源对外部用户并不友好，为了更好地让大家聚焦于微服务，我们以 CloudWeGo 作为项目名，逐步将内部微服务体系的项目开源，内外统一使用开源库，各项目以开源库为主进行迭代。\\n\\n内外维护一套代码，统一迭代演进，是我们开源前明确的原则，但毕竟涉及到代码库的调整，我们要保证内部用户尽可能无感知的迁移到开源库，本着对内部和开源用户负责的态度，我们要先确认内部可以平滑过渡，所以开源时并未对外宣传。 让我们欣慰的是，在未宣传的情况下，一个月内 [Kitex](https://github.com/cloudwego/kitex) 收获了 1.2k stars，[Netpoll](https://github.com/cloudwego/netpoll) 收获了 700+ stars。\\n\\nCloudWeGo 不仅仅是一个对外的开源项目，也是一个真实的超大规模企业级实践项目。\\n\\n我们希望通过 CloudWeGo 丰富云原生社区的 Golang 产品体系，助力其他企业快速构建云原生架构，也希望吸引外部开发者共建，促进面向多元场景支持的演进，丰富产品能力。\\n\\n因为 CloudWeGo 下的项目会依赖很多内部的基础工具库，我们也推动将内部常用的 Golang 基础工具库开源出去，统一在 [bytedance/gopkg](https://github.com/bytedance/gopkg) 维护。\\n\\n## CloudWeGo 开源项目\\n\\nCloudWeGo 第一批以 [Kitex](https://github.com/cloudwego/kitex) RPC 框架和 [Netpoll](https://github.c...(content trimmed)\"\n    }\n  ]\n}\n\n\n来自工具 web-search 的结果:\n{\n  \"query\": \"ByteDance 开源项目 贡献者 背景信息\",\n  \"engine\": \"google\",\n  \"totalResults\": 3,\n  \"results\": [\n    {\n      \"index\": 1,\n      \"title\": \"字节跳动开源官网\",\n      \"url\": \"https://opensource.bytedance.com/\",\n      \"content\": \"\"\n    },\n    {\n      \"index\": 2,\n      \"title\": \"Docs\",\n      \"url\": \"https://docs.feishu.cn/v/wiki/CaHIw9Cjhi6b9okTrdqcEJBQn6g/a7\",\n      \"content\": \"字节跳动自 2012 年成立伊始，就将数据挖掘和推荐引擎技术作为公司产品的核心，推出基于智能推荐算法的资讯产品「今日头条」，它是国内最早把人工智能技术运用到移动应用场景中的个性化内容推荐产品之一。​\\n\\n梁汝波在年会中提到，字节跳动在2023年才开始布局AI，“已经是太晚了”。根据界面报道，去年一年，字节跳动创始人张一鸣将主要精力都花在了AI上。2024年2月初，张楠宣布卸任抖音集团CEO的，转战AI生成式视频领域的剪映。这无一不显示，字节将迅速回归AI的主业。​\\n\\n过去这半年，字节在 AI 应用尤其是大模型赛道火力全开。内部完成了组织架构调整，全面押注 AI 原生应用，连续推出了十多款基于 AI 和大模型的产品，让整个公司快速升级为 “超级 AI 应用工厂”。目前已经初见成效，占领应用场景的目标非常明确。​\\n\\n本文完整梳理了字节AI团队架构和发展沿革，字节在AI大模型和产品上的布局和数据表现，同时也推测了字节可能上线的新产品。Enjoy！​\\n\\n如果想获取全部字节AI相关资料，关注公众号并发送“字节AI”和本文朋友圈分享截图即可获取​\\n\\n2023年的1月，字节跳动组建了首个专注于大型模型研发的团队，该团队由其搜索部门主导，朱文佳担任负责人。朱文佳此前在TikTok担任技术负责人，并在加入字节跳动之前在百度担任搜索部主任架构师。2019年，朱文佳曾担任今日头条的CEO，后于2021年转任TikTok产品技术负责人。​\\n\\n该团队由两个小组构成，一个小组来自字节跳动的原搜索部门，负责开发语言模型；另一个小组属于产品研发与工程架构部下的智能创作团队，负责图片模型的开发。陆游，抖音的社交负责人，也在2023年上半年加入了这个大型模型团队。​\"\n    },\n    {\n      \"index\": 3,\n      \"title\": \"字节跳动开源内部微服务中间件 CloudWeGo\",\n      \"url\": \"https://www.cloudwego.io/zh/blog/2021/09/07/%E5%AD%97%E8%8A%82%E8%B7%B3%E5%8A%A8%E5%BC%80%E6%BA%90%E5%86%85%E9%83%A8%E5%BE%AE%E6%9C%8D%E5%8A%A1%E4%B8%AD%E9%97%B4%E4%BB%B6-cloudwego/\",\n      \"content\": \"字节跳动宣布正式开源内部微服务中间件 CloudWeGo\\n\\n## 开源背景\\n\\n[CloudWeGo](https://github.com/cloudwego) 是一套由字节跳动开源的、以 Go 语言为核心的、可快速构建企业级云原生架构的中间件集合，专注于微服务通信与治理，具备高性能、可扩展、高可靠的特点。\\n\\n字节跳动内部使用 Golang 作为主要的业务开发语言，我们支持着数万个 Golang 微服务的可靠通信，经过数量众多的微服务和海量流量的验证，我们已经有了较为成熟的微服务最佳实践，于是考虑将内部的实践开源出去丰富社区生态。 但微服务相关的项目较多，每个项目单独开源对外部用户并不友好，为了更好地让大家聚焦于微服务，我们以 CloudWeGo 作为项目名，逐步将内部微服务体系的项目开源，内外统一使用开源库，各项目以开源库为主进行迭代。\\n\\n内外维护一套代码，统一迭代演进，是我们开源前明确的原则，但毕竟涉及到代码库的调整，我们要保证内部用户尽可能无感知的迁移到开源库，本着对内部和开源用户负责的态度，我们要先确认内部可以平滑过渡，所以开源时并未对外宣传。 让我们欣慰的是，在未宣传的情况下，一个月内 [Kitex](https://github.com/cloudwego/kitex) 收获了 1.2k stars，[Netpoll](https://github.com/cloudwego/netpoll) 收获了 700+ stars。\\n\\nCloudWeGo 不仅仅是一个对外的开源项目，也是一个真实的超大规模企业级实践项目。\\n\\n我们希望通过 CloudWeGo 丰富云原生社区的 Golang 产品体系，助力其他企业快速构建云原生架构，也希望吸引外部开发者共建，促进面向多元场景支持的演进，丰富产品能力。\\n\\n因为 CloudWeGo 下的项目会依赖很多内部的基础工具库，我们也推动将内部常用的 Golang 基础工具库开源出去，统一在 [bytedance/gopkg](https://github.com/bytedance/gopkg) 维护。\\n\\n## CloudWeGo 开源项目\\n\\nCloudWeGo 第一批以 [Kitex](https://github.com/cloudwego/kitex) RPC 框架和 [Netpoll](https://github.c...(content trimmed)\"\n    }\n  ]\n}\n\n\n\n请基于以上所有信息生成一份全面、详细的研究报告，确保包含所有重要的数据点和见解。"}], "max_tokens": 10000, "thinking": {"type": "disabled"}}, "baseURL": "https://ark.cn-beijing.volces.com/api/v3"}