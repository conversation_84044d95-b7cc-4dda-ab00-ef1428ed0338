# UI-TARS Agent 服务器部署配置
version: '3.8'

services:
  ui-tars-agent:
    build: .
    ports:
      - "3000:3000"
    environment:
      # 模型配置
      - MODEL_PROVIDER=volcengine
      - MODEL_API_KEY=aa3fac83-29e2-4f45-a3ca-489a77c1534c
      - MODEL_BASE_URL=https://ark.cn-beijing.volces.com/api/v3
      - MODEL_NAME=ep-20250730231730-swwgj
      
      # 浏览器配置
      - BROWSER_HEADLESS=true
      - BROWSER_CONTROL=hybrid
      
      # 服务配置
      - NODE_ENV=production
      - LOG_LEVEL=info
      
    volumes:
      # 挂载工作目录（可选）
      - ./workspace:/app/workspace
      # 挂载日志目录
      - ./logs:/app/logs
      
    restart: unless-stopped
    
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      
    # 资源限制
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'

  # 可选：添加反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - ui-tars-agent
    restart: unless-stopped
