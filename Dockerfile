# UI-TARS Agent 服务器部署 Dockerfile
FROM node:22-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    # 浏览器依赖
    chromium \
    # 图像处理依赖
    libx11-dev \
    libxtst6 \
    libxrandr2 \
    libasound2 \
    libpangocairo-1.0-0 \
    libatk1.0-0 \
    libcairo-gobject2 \
    libgtk-3-0 \
    libgdk-pixbuf2.0-0 \
    # 清理缓存
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制配置文件
COPY package*.json ./
COPY agent-tars.config.js ./
COPY .env ./

# 安装依赖
RUN npm install -g @agent-tars/cli@latest

# 设置环境变量
ENV DISPLAY=:99
ENV CHROME_BIN=/usr/bin/chromium
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true

# 暴露端口
EXPOSE 3000

# 启动命令
CMD ["npx", "@agent-tars/cli", "serve", "--config", "agent-tars.config.js", "--port", "3000", "--host", "0.0.0.0"]
