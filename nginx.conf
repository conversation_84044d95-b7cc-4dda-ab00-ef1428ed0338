# Nginx 反向代理配置
events {
    worker_connections 1024;
}

http {
    upstream ui_tars_backend {
        server ui-tars-agent:3000;
    }

    # 限流配置
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;

    server {
        listen 80;
        server_name your-domain.com;

        # API 路由
        location /api/ {
            # 限流
            limit_req zone=api burst=20 nodelay;
            
            # 代理到后端
            proxy_pass http://ui_tars_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 超时设置
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 300s;
            
            # 请求体大小限制
            client_max_body_size 10M;
        }

        # 健康检查
        location /health {
            proxy_pass http://ui_tars_backend/health;
        }

        # 静态文件（如果有Web界面）
        location / {
            proxy_pass http://ui_tars_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
    }

    # HTTPS 配置（可选）
    # server {
    #     listen 443 ssl;
    #     server_name your-domain.com;
    #     
    #     ssl_certificate /etc/nginx/ssl/cert.pem;
    #     ssl_certificate_key /etc/nginx/ssl/key.pem;
    #     
    #     location / {
    #         proxy_pass http://ui_tars_backend;
    #         # ... 其他配置同上
    #     }
    # }
}
