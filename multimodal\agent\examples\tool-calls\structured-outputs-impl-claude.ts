/*
 * Copyright (c) 2025 Bytedance, Inc. and its affiliates.
 * SPDX-License-Identifier: Apache-2.0
 */

/**
 * An example of a tool call specifically using Claude models via Azure OpenAI interface.
 */

import { Agent, AgentRunNonStreamingOptions, Tool, z } from '../../src';

const locationTool = new Tool({
  id: 'getCurrentLocation',
  description: "Get user's current location",
  parameters: z.object({}),
  function: async () => {
    return { location: 'Boston' };
  },
});

const weatherTool = new Tool({
  id: 'getWeather',
  description: 'Get weather information for a specified location',
  parameters: z.object({
    location: z.string().describe('Location name, such as city name'),
  }),
  function: async (input) => {
    const { location } = input;
    return {
      location,
      temperature: '70°F (21°C)',
      condition: 'Sunny',
      precipitation: '10%',
      humidity: '45%',
      wind: '5 mph',
    };
  },
});

export const agent = new Agent({
  model: {
    provider: 'azure-openai',
    id: 'aws_sdk_claude37_sonnet',
    baseURL: process.env.AWS_CLAUDE_API_BASE_URL,
  },
  tools: [locationTool, weatherTool],
  toolCallEngine: 'structured_outputs',
});

export const runOptions: AgentRunNonStreamingOptions = {
  input: "How's the weather today?",
};

async function main() {
  const answer = await agent.run(runOptions);
  console.log(answer);
}

if (require.main === module) {
  main();
}
