{"model": {"provider": "volcengine", "id": "ep-20250730231730-swwgj", "apiKey": "aa3fac83-29e2-4f45-a3ca-489a77c1534c", "baseURL": "https://ark.cn-beijing.volces.com/api/v3"}, "workspace": {"workingDirectory": "."}, "browser": {"type": "local", "headless": false, "control": "hybrid"}, "search": {"provider": "browser_search", "count": 10, "browserSearch": {"engine": "google", "needVisitedUrls": false}}, "mcpImpl": "in-memory", "mcpServers": {}, "maxIterations": 100, "maxTokens": 8192, "enableStreamingToolCallEvents": true, "experimental": {"dumpMessageHistory": false}, "server": {"port": 3000, "host": "localhost"}, "logLevel": "info"}