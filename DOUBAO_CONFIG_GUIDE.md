# Doubao 1.5 Thinking Vision Pro 配置指南

本指南将帮助你在 UI-TARS-desktop 项目中配置 Doubao 1.5 Thinking Vision Pro 模型。

## 配置信息

根据你提供的火山引擎调用示例，我已经为你创建了以下配置：

- **模型端点**: `ep-20250730231730-swwgj`
- **API密钥**: `aa3fac83-29e2-4f45-a3ca-489a77c1534c`
- **基础URL**: `https://ark.cn-beijing.volces.com/api/v3`
- **提供商**: `volcengine`

## 配置文件

我已经为你创建了三个配置文件：

### 1. `.env` 文件
环境变量配置，适合开发环境使用。

### 2. `agent-tars.config.js` 文件
JavaScript配置文件，功能最全面，支持动态配置。

### 3. `agent-tars.config.json` 文件
JSON配置文件，简单易读，适合静态配置。

## 使用方法

### 方法一：使用命令行参数（推荐）

```bash
# 直接使用命令行参数启动
npx @agent-tars/cli@latest --provider volcengine --model ep-20250730231730-swwgj --apiKey aa3fac83-29e2-4f45-a3ca-489a77c1534c --baseURL https://ark.cn-beijing.volces.com/api/v3

# 或者如果已全局安装
agent-tars --provider volcengine --model ep-20250730231730-swwgj --apiKey aa3fac83-29e2-4f45-a3ca-489a77c1534c --baseURL https://ark.cn-beijing.volces.com/api/v3
```

### 方法二：使用配置文件

```bash
# 使用JavaScript配置文件
npx @agent-tars/cli@latest --config agent-tars.config.js

# 使用JSON配置文件
npx @agent-tars/cli@latest --config agent-tars.config.json
```

### 方法三：使用环境变量

如果使用 `.env` 文件，Agent TARS 会自动读取环境变量。

## 配置说明

### 模型配置
- `provider`: 设置为 `volcengine`（火山引擎）
- `id`: 你的模型端点ID
- `apiKey`: 你的API密钥
- `baseURL`: 火山引擎API基础URL

### 浏览器配置
- `headless`: 是否无头模式运行（false = 显示浏览器界面）
- `control`: 控制模式
  - `hybrid`: 混合模式（推荐），结合DOM和视觉识别
  - `dom`: 纯DOM模式
  - `visual-grounding`: 纯视觉识别模式

### 搜索配置
- `provider`: 搜索提供商（browser_search 使用浏览器搜索）
- `engine`: 搜索引擎（google, bing, baidu, sogou）

## 验证配置

启动后，你应该能看到类似以下的输出：
```
✓ Model provider: volcengine
✓ Model: ep-20250730231730-swwgj
✓ API connection established
```

## 注意事项

1. **API密钥安全**: 请确保不要将包含API密钥的配置文件提交到版本控制系统
2. **网络连接**: 确保你的网络可以访问火山引擎API
3. **模型权限**: 确认你的API密钥有权限访问指定的模型端点
4. **Token限制**: 注意模型的token使用限制

## 故障排除

如果遇到问题，请检查：

1. API密钥是否正确
2. 模型端点ID是否正确
3. 网络连接是否正常
4. 是否有足够的API配额

## 高级配置

### 启用思维链功能

Doubao 1.5 Thinking Vision Pro 支持思维链推理，可以在配置中启用：

```javascript
// 在 agent-tars.config.js 中添加
model: {
  // ... 其他配置
  thinking: {
    type: 'enabled', // 启用思维链
    budgetTokens: 4000 // 思维链token预算
  }
}
```

### 多模态配置

该模型支持图像输入，Agent TARS 会自动处理屏幕截图和图像分析。

## 支持

如果需要更多帮助，请参考：
- [Agent TARS 官方文档](https://agent-tars.com)
- [火山引擎文档](https://www.volcengine.com/docs)
