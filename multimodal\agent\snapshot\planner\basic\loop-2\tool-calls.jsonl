[{"toolCallId": "call_1749894965602_uj2x6", "name": "visit-link", "args": {"url": "https://blog.csdn.net/interpromotion/article/details/147456150"}, "result": {"title": "字节跳动在GitHub上有哪些开源项目_字节开源项目-CSDN博客", "url": "https://blog.csdn.net/interpromotion/article/details/147456150", "excerpt": "文章浏览阅读2.9k次，点赞24次，收藏33次。字节跳动（ByteDance）在GitHub上开源了许多项目，涵盖前端、后端、云原生、AI、数据库等多个领域。这些项目体现了字节跳动在技术领域的广泛投入，适合开发者学习或直接应用于生产环境。_字节开源项目", "content": "![](https://csdnimg.cn/release/blogv2/dist/pc/img/original.png)\n\n[程序员差不多先生](https://blog.csdn.net/interpromotion \"程序员差不多先生\") ![](https://csdnimg.cn/release/blogv2/dist/pc/img/newCurrentTime2.png) 于 2025-04-23 15:44:05 发布\n\n版权声明：本文为博主原创文章，遵循 [CC 4.0 BY-SA](http://creativecommons.org/licenses/by-sa/4.0/) 版权协议，转载请附上原文出处链接和本声明。\n\n字节跳动（ByteDance）在GitHub上开源了许多项目，涵盖前端、后端、云原生、AI、数据库等多个领域。以下是一些典型项目及其简介：\n\n* * *\n\n#### **1\\. 前端 & 跨平台开发**\n\n*   **Hippy**\n    \n    *   **仓库**: [Tencent/Hippy](https://github.com/Tencent/Hippy)（注：Hippy 最初由腾讯开源，但字节跳动内部有深度定制和扩展版本）\n    *   **简介**: 高性能跨端开发框架，支持 React/Vue 语法，广泛用于抖音、今日头条等 App 内嵌页面。\n*   **IconPark**\n    \n    *   **仓库**: [bytedance/IconPark](https://github.com/bytedance/IconPark)\n    *   **简介**: 开源图标库，提供超过 2,000 个高质量图标，支持按需定制颜色、大小、线条风格等。\n*   **Semi Design**\n    \n    *   **仓库**: [DouyinFE/semi-design](https://github.com/DouyinFE/semi-design)\n    *   **简介**: 抖音前端团队开源的现代企业级 UI 设计系统，支持 React 组件库与设计工具集成。\n\n* * *\n\n#### **2\\. 云原生 & 微服务**\n\n*   **CloudWeGo**\n    \n    *   **仓库**: [cloudwego](https://github.com/cloudwego)\n    *   **简介**: 字节跳动开源的云原生微服务生态，核心项目包括：\n        *   **Kitex**: 高性能 RPC 框架（Golang）。\n        *   **Hertz**: 高性能 HTTP 框架（Golang）。\n        *   **Netpoll**: 基于 epoll 的高性能网络库（Golang）。\n*   **KubeWharf**\n    \n    *   **仓库**: [kubewharf](https://github.com/kubewharf)\n    *   **简介**: Kubernetes 增强套件，解决大规模集群管理问题，包含 **Katalyst**（资源调度优化）、**KubeAdmiral**（多集群管理）等子项目。\n\n* * *\n\n#### **3\\. 数据库 & 存储**\n\n*   **ByteGraph**\n    \n    *   **仓库**: [bytedance/bytegraph](https://github.com/bytedance/bytegraph)\n    *   **简介**: 分布式图数据库，支持海量数据存储与复杂查询，用于抖音社交关系图谱等场景。\n*   **BytedKV**\n    \n    *   **仓库**: [bytedance/bytedkv](https://github.com/bytedance/bytedkv)\n    *   **简介**: 高性能分布式 KV 存储系统，支持强一致性和水平扩展。\n\n* * *\n\n#### **4\\. AI & 机器学习**\n\n*   **ByteMLPerf**\n    \n    *   **仓库**: [bytemlperf](https://github.com/bytemlperf)\n    *   **简介**: 机器学习推理性能优化工具，支持多种硬件加速（GPU/TPU/CPU）。\n*   **MegaScale**\n    \n    *   **仓库**: [bytedance/megascale](https://github.com/bytedance/megascale)\n    *   **简介**: 大模型训练框架，优化千卡级 GPU 集群的并行训练效率。\n\n* * *\n\n#### **5\\. 开发工具 & 中间件**\n\n*   **Arco Design**\n    \n    *   **仓库**: [arco-design](https://github.com/arco-design)\n    *   **简介**: 企业级设计系统，包含 React/Vue 组件库与设计规范（注：由字节跳动中国业务团队维护）。\n*   **MonkeyType**\n    \n    *   **仓库**: [instagram/MonkeyType](https://github.com/instagram/MonkeyType)\n    *   **简介**: Python 类型注解自动生成工具（字节跳动参与贡献）。\n\n* * *\n\n#### **6\\. 多媒体处理**\n\n*   **BVC (Bytedance Video Codec)**\n    *   **仓库**: [bytedance/bvc](https://github.com/bytedance/bvc)\n    *   **简介**: 高性能视频编解码器，针对移动端和实时场景优化。\n\n* * *\n\n#### **7\\. 测试与监控**\n\n*   **Dynoscope**\n    *   **仓库**: [bytedance/dynoscope](https://github.com/bytedance/dynoscope)\n    *   **简介**: 动态追踪与性能分析工具，用于诊断分布式系统问题。\n\n* * *\n\n#### **访问方式**\n\n*   字节跳动 GitHub 组织主页：[https://github.com/bytedance](https://github.com/bytedance)\n*   部分项目可能分散在其他关联组织（如 [cloudwego](https://github.com/cloudwego)、[kubewharf](https://github.com/kubewharf)）。\n\n这些项目体现了字节跳动在技术领域的广泛投入，适合开发者学习或直接应用于生产环境。"}, "executionTime": 6146}]