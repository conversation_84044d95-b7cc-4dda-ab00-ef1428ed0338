{"name": "@multimodal/agent-server", "version": "0.3.0-beta.1", "description": "Server for Multimodal Agent.", "main": "dist/index.js", "types": "dist/index.d.ts", "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org"}, "files": ["dist"], "scripts": {"dev": "rslib build --watch", "build": "rslib build", "prepublishOnly": "pnpm run build", "test": "vitest run", "test:watch": "vitest"}, "dependencies": {"@agent-tars/core": "workspace:*"}, "devDependencies": {"@agent-tars/interface": "workspace:*", "@multimodal/agent-snapshot": "workspace:*", "@multimodal/agio": "workspace:*", "cors": "^2.8.5", "express": "4.21.2", "http-proxy-middleware": "^2.0.6", "socket.io": "^4.7.2", "lowdb": "^6.0.1", "nanoid": "^5.0.8", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@rslib/core": "0.10.0", "@types/node": "22.15.30", "typescript": "^5.5.3", "vitest": "3.2.4"}}